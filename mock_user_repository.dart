class MockUserRepository {
  // تخزين المؤقت للمستخدمين (سيتم فقدانه عند إعادة تشغيل التطبيق)
  static final Map<String, String> _mockUsers = {
    '<EMAIL>': '123456', // email: password
    '<EMAIL>': 'password123',
  };

  // تسجيل الدخول مع تحسينات الأمان
  static Future<bool> login(String email, String password) async {
    try {
      // محاكاة اتصال بالخادم
      await Future.delayed(const Duration(milliseconds: 800));
      
      // تنظيف المدخلات
      final cleanedEmail = email.trim().toLowerCase();
      
      // التحقق من وجود المستخدم
      if (!_mockUsers.containsKey(cleanedEmail)) {
        return false;
      }
      
      // مقارنة كلمات المرور (في الواقع يجب استخدام hashing)
      return _mockUsers[cleanedEmail] == password;
    }  catch (e) {
    return false;
  }
  }

  // التسجيل مع تحسينات التحقق
  static Future<({bool success, String message})> register(
    String email, 
    String password,
  ) async {
    try {
      await Future.delayed(const Duration(milliseconds: 800));
      
      final cleanedEmail = email.trim().toLowerCase();
      
      // التحقق من صحة البريد الإلكتروني
      if (!RegExp(r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$').hasMatch(cleanedEmail)) {
        return (success: false, message: 'بريد إلكتروني غير صالح');
      }
      
      // التحقق من قوة كلمة المرور
      if (password.length < 6) {
        return (success: false, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      }
      
      // التحقق من وجود المستخدم مسبقاً
      if (_mockUsers.containsKey(cleanedEmail)) {
        return (success: false, message: 'البريد الإلكتروني مستخدم بالفعل');
      }
      
      // في الواقع يجب تخزين hash بدلاً من كلمة المرور مباشرة
      _mockUsers[cleanedEmail] = password;
      
      return (success: true, message: 'تم التسجيل بنجاح');
    } catch (e) {
    return (success: false, message: 'حدث خطأ أثناء التسجيل');
  }
  }

  // دالة مساعدة لحذف المستخدم (لأغراض الاختبار)
  static void deleteUser(String email) {
    _mockUsers.remove(email.trim().toLowerCase());
  }
}