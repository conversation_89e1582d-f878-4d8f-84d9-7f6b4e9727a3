import 'package:flutter/material.dart';

class FoodSelectionScreen extends StatelessWidget {
  const FoodSelectionScreen({super.key});

  // Map foods to their image asset paths
  final Map<String, String> foodImages = const {
    'خيار': 'assets/images/food/11.jpg',
    'سلطة': 'assets/images/food/2.jpg',
    'دجاج': 'assets/images/food/3.jpg',
    'بانيه': 'assets/images/food/10.jpg',
    'كشري': 'assets/images/food/5.jpg',
    'مكرونه': 'assets/images/food/4.jpg',
    'جزر': 'assets/images/food/12.jpg',
    'لحم': 'assets/images/food/8.jpg',
    'ملوخيه': 'assets/images/food/6.jpg',
    'برجر دجاج': 'assets/images/food/7.jpg',
    'بيتزا': 'assets/images/food/9.jpg',
    'خس': 'assets/images/food/13.jpg',

  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('أطعمة')),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: GridView.count(
          crossAxisCount: 2,
          childAspectRatio: 1.5,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          children:
              foodImages.entries.map((entry) {
                return FoodCard(food: entry.key, imagePath: entry.value);
              }).toList(),
        ),
      ),
    );
  }
}

class FoodCard extends StatelessWidget {
  final String food;
  final String imagePath;

  const FoodCard({super.key, required this.food, required this.imagePath});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            imagePath,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder:
                (_, __, ___) =>
                    const Icon(Icons.fastfood, size: 40, color: Colors.blue),
          ),
          const SizedBox(height: 10),
          Text(
            food,
            style: const TextStyle(fontSize: 18),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
