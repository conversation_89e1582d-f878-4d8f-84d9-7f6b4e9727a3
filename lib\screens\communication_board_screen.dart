import 'package:flutter/material.dart';
import 'emotion_selection_screen.dart';
import 'action_selection_screen.dart';
import 'location_selection_screen.dart';
import 'drink_selection_screen.dart';
import 'food_selection_screen.dart';

class CommunicationBoardScreen extends StatelessWidget {
  const CommunicationBoardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('لوحة التواصل')),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            const Text('أنا عاوز', style: TextStyle(fontSize: 24)),
            const SizedBox(height: 20),
            Expanded(
              child: <PERSON><PERSON><PERSON>iew(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                  childAspectRatio: 1.5,
                ),
                children: [
                  _buildCategoryCard(
                    context, 
                    'مشاعر', 
                    'assets/images/feelings/8.jpg', 
                    const EmotionSelectionScreen()
                  ),
                  _buildCategoryCard(
                    context, 
                    'أفعال', 
                    'assets/images/11.jpg', 
                    const ActionSelectionScreen()
                  ),
                  _buildCategoryCard(
                    context, 
                    'أماكن', 
                    'assets/images/14.jpeg', 
                    const LocationSelectionScreen()
                  ),
                  _buildCategoryCard(
                    context, 
                    'مشروبات', 
                    'assets/images/16.png', 
                    const DrinkSelectionScreen()
                  ),
                  _buildCategoryCard(
                    context, 
                    'أطعمة', 
                    'assets/images/20.png', 
                    const FoodSelectionScreen()
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(
    BuildContext context, 
    String title, 
    String imagePath, 
    Widget screen
  ) {
    return Card(
      child: InkWell(
        onTap: () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => screen));
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              imagePath,
              width: 60,
              height: 60,
              fit: BoxFit.cover,
              errorBuilder: (_, __, ___) => const Icon(Icons.image, size: 40, color: Colors.grey),
            ),
            const SizedBox(height: 10),
            Text(title, style: const TextStyle(fontSize: 18)),
          ],
        ),
      ),
    );
  }
}