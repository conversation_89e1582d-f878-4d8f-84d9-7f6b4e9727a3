import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';

class LocationSelectionScreen extends StatelessWidget {
  const LocationSelectionScreen({super.key});

  final Map<String, String> locationImages = const {
    'غرفة السفرة': 'assets/images/places/1.jpg',
    'الحمام': 'assets/images/places/4.jpg',
    'المطبخ': 'assets/images/places/3.jpg',
    'المدرسة': 'assets/images/places/5.jpg',
    'الشاطئ': 'assets/images/places/6.jpg',
    'المسجد': 'assets/images/places/8.jpg',
    'الماركت': 'assets/images/places/7.jpg',
    'غرفة المعيشة': 'assets/images/places/9.jpg',
    'غرفه النوم': 'assets/images/places/2.jpg',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('أماكن')),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: GridView.count(
          crossAxisCount: 2,
          childAspectRatio: 1.5,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          children: locationImages.entries.map((entry) {
            return LocationCard(
              location: entry.key,
              imagePath: entry.value,
              soundPath: 'assets/sounds/places/Recording.mp3', // Pass the sound path
            );
          }).toList(),
        ),
      ),
    );
  }
}

class LocationCard extends StatelessWidget {
  final String location;
  final String imagePath;
  final String soundPath;

  const LocationCard({
    super.key,
    required this.location,
    required this.imagePath,
    required this.soundPath,
  });

  @override
  Widget build(BuildContext context) {
    final player = AudioPlayer();

    return Card(
      child: InkWell(
        onTap: () async {
          await player.play(AssetSource(soundPath));
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              imagePath,
              width: 60,
              height: 60,
              fit: BoxFit.cover,
              errorBuilder: (_, __, ___) => const Icon(Icons.place, size: 40, color: Colors.blue),
            ),
            const SizedBox(height: 10),
            Text(
              location,
              style: const TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}