name: arabic_communication_app
description: "An Arabic communication assistance app for non-verbal individuals."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.7.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  audioplayers: ^5.2.1
  sqflite: ^2.3.2
  path: ^1.8.3
  flutter_tts: ^3.8.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/intro.jpg
    - assets/images/
    - assets/images/actions/
    - assets/images/drinks/
    - assets/images/feelings/
    - assets/images/food/
    - assets/images/places/
    - assets/images/1.jpg
    - assets/images/2.jpg
    - assets/images/3.jpg
    - assets/images/4.jpg
    - assets/images/6.jpg
    - assets/images/7.jpg
    - assets/images/8.jpg
    - assets/images/9.jpg
    - assets/images/10.jpg
    - assets/images/11.jpg
    - assets/sounds/
    - assets/sounds/places/Recording.mp3

  fonts:
    - family: <PERSON><PERSON><PERSON>
      fonts:
        - asset: fonts/Tajawal-Regular.ttf
        - asset: fonts/Tajawal-Medium.ttf
          weight: 500
        - asset: fonts/Tajawal-Bold.ttf
          weight: 700