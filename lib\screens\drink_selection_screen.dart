import 'package:flutter/material.dart';

class DrinkSelectionScreen extends StatelessWidget {
  const DrinkSelectionScreen({super.key});

  // Map drinks to their image asset paths
  final Map<String, String> drinkImages = const {
    'عصير برتقال': 'assets/images/drinks/2.jpg',
    'عصير تفاح': 'assets/images/drinks/1.jpg',
    'عصير ليمون': 'assets/images/drinks/5.jpg',
    'عصير مانجا': 'assets/images/drinks/9.jpg',
    'عصير فراولة': 'assets/images/drinks/7.jpg',
    'ماء': 'assets/images/drinks/8.jpg',
    'شاي بلبن': 'assets/images/drinks/3.jpg',
    'لبن': 'assets/images/drinks/4.jpg',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('مشروبات')),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: GridView.count(
          crossAxisCount: 2,
          childAspectRatio: 1.0,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          children: drinkImages.entries.map((entry) {
            return DrinkCard(
              drink: entry.key,
              imagePath: entry.value,
            );
          }).toList(),
        ),
      ),
    );
  }
}

class DrinkCard extends StatelessWidget {
  final String drink;
  final String imagePath;

  const DrinkCard({
    super.key,
    required this.drink,
    required this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            imagePath,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (_, __, ___) => const Icon(Icons.local_drink, size: 40, color: Colors.blue),
          ),
          const SizedBox(height: 10),
          Text(
            drink,
            style: const TextStyle(fontSize: 18),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}