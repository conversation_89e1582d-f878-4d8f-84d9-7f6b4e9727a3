import 'package:flutter/material.dart';

class ActionSelectionScreen extends StatelessWidget {
  const ActionSelectionScreen({super.key});

  // Map actions to their image filenames (ONLY filenames)
  final Map<String, String> actionImages = const {
    'أشرب': '10.jpg',
    'العب': '3.jpg',
    'أغسل يدي': '8.jpg',
    'أكل': '4.jpg',
    'أذهب': '2.jpg',
    'أنام': '1.jpg',
    'ألبس': '9.jpg',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('أفعال'), centerTitle: true),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          childAspectRatio: 0.9,
          crossAxisSpacing: 15,
          mainAxisSpacing: 15,
          children:
              actionImages.entries.map((entry) {
                return ActionCard(
                  action: entry.key,
                  imagePath:
                      'assets/images/actions/${entry.value}', // Combine path here
                );
              }).toList(),
        ),
      ),
    );
  }
}

class ActionCard extends StatelessWidget {
  final String action;
  final String imagePath;

  const ActionCard({super.key, required this.action, required this.imagePath});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            imagePath,
            width: 60,
            height: 60,
            errorBuilder: (context, error, stackTrace) {
              // Image loading failed, show default icon
              return Icon(
                Icons.accessibility,
                size: 50,
                color: Colors.blue[700],
              );
            },
          ),
          const SizedBox(height: 10),
          Text(
            action,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
