{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AndroidStudioProjects\\arabic_communication_app\\android\\app\\.cxx\\Debug\\651o4g3p\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AndroidStudioProjects\\arabic_communication_app\\android\\app\\.cxx\\Debug\\651o4g3p\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}