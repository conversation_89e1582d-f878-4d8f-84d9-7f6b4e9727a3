import 'package:flutter/material.dart';

class EmotionSelectionScreen extends StatelessWidget {
  const EmotionSelectionScreen({super.key});

  // Map emotions to their image filenames
  final Map<String, String> emotionImages = const {
    'حزين': 'assets/images/feelings/1.jpg',
    'سعيد': 'assets/images/feelings/3.jpg',
    'خجول': 'assets/images/feelings/5.jpg',
    'غضبان': 'assets/images/feelings/6.jpg',
    'نعسان': 'assets/images/feelings/2.jpg',
    'هادئ': 'assets/images/feelings/7.jpg',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مشاعر'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                childAspectRatio: 0.8, // Better for image + text
                crossAxisSpacing: 15,
                mainAxisSpacing: 15,
                children: emotionImages.entries.map((entry) {
                  return EmotionCard(
                    emotion: entry.key,
                    imagePath: entry.value, // FIXED: use the correct path
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class EmotionCard extends StatelessWidget {
  final String emotion;
  final String imagePath;

  const EmotionCard({
    super.key,
    required this.emotion,
    required this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            imagePath,
            width: 70,
            height: 70,
            errorBuilder: (_, __, ___) => const Icon(
              Icons.emoji_emotions,
              size: 50,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            emotion,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}